import type { AppRouterClient } from "@/lib/orpc";
import type { InferClientBodyOutputs } from "@orpc/client";

type BodyOutputs = InferClientBodyOutputs<AppRouterClient>;

export type User = BodyOutputs["user"]["getCurrentUserInfo"];

export type UserType = User["userType"];
// export type UserType = "admin" | "student" | "prefect";

export type UserStat = "strength" | "intelligence" | "dexterity" | "defence" | "endurance" | "vitality";

export type StatusEffectList = BodyOutputs["user"]["getStatusEffects"];
export type StatusEffect = StatusEffectList[number];

export interface TrainStatRequest {
    stat: UserStat;
    focusAmount: number;
}

export interface TrainStatResponse {
    statProgress: {
        stat: string;
        expGained: number;
        leveledUp: boolean;
        levelsGained: number;
        currentLevel: number;
        currentExp: number;
        expToNextLevel: number;
        previousLevel: number;
    };
    focusRemaining: number;
    dailyFatigueRemaining: number;
}
